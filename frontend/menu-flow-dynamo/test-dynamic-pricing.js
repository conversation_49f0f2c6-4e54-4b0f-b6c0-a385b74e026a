// Test script for dynamic pricing functionality
// Run this in the browser console to test the fixes

async function testDynamicPricing() {
  console.log('🧪 Testing Dynamic Pricing Functionality...');
  
  // Test restaurant ID (La Pepica Restaurant from the screenshots)
  const testRestaurantId = '1fd5e254-ab0e-468b-adfe-22217536eee6';
  
  try {
    // Import the functions we need to test
    const { setDynamicPricingEnabled, isDynamicPricingEnabled, updatePricingRules } = 
      await import('./src/services/dynamicPricingService.ts');
    
    console.log('✅ Successfully imported dynamic pricing functions');
    
    // Test 1: Check current status
    console.log('\n📋 Test 1: Checking current dynamic pricing status...');
    const currentStatus = await isDynamicPricingEnabled(testRestaurantId);
    console.log(`Current status: ${currentStatus ? 'ENABLED' : 'DISABLED'}`);
    
    // Test 2: Enable dynamic pricing
    console.log('\n📋 Test 2: Enabling dynamic pricing...');
    const enableResult = await setDynamicPricingEnabled(true, testRestaurantId);
    console.log(`Enable result: ${enableResult ? 'SUCCESS' : 'FAILED'}`);
    
    // Test 3: Verify it's enabled
    console.log('\n📋 Test 3: Verifying dynamic pricing is enabled...');
    const verifyEnabled = await isDynamicPricingEnabled(testRestaurantId);
    console.log(`Verification result: ${verifyEnabled ? 'ENABLED' : 'DISABLED'}`);
    
    // Test 4: Update pricing rules
    console.log('\n📋 Test 4: Updating pricing rules...');
    const testRules = {
      highTraffic: { threshold: 0.8, percentage: 15 },
      mediumTraffic: { threshold: 0.5, percentage: 8 },
      lowTraffic: { threshold: 0.3, percentage: -10 },
      applyToCategories: { food: true, drinks: true },
      confidenceThreshold: 0.7
    };
    
    const updateResult = await updatePricingRules(testRestaurantId, testRules);
    console.log(`Update rules result: ${updateResult ? 'SUCCESS' : 'FAILED'}`);
    
    // Test 5: Disable dynamic pricing
    console.log('\n📋 Test 5: Disabling dynamic pricing...');
    const disableResult = await setDynamicPricingEnabled(false, testRestaurantId);
    console.log(`Disable result: ${disableResult ? 'SUCCESS' : 'FAILED'}`);
    
    // Test 6: Verify it's disabled
    console.log('\n📋 Test 6: Verifying dynamic pricing is disabled...');
    const verifyDisabled = await isDynamicPricingEnabled(testRestaurantId);
    console.log(`Verification result: ${verifyDisabled ? 'ENABLED' : 'DISABLED'}`);
    
    console.log('\n🎉 Dynamic Pricing Test Complete!');
    console.log('Summary:');
    console.log(`- Enable: ${enableResult ? '✅' : '❌'}`);
    console.log(`- Verify Enable: ${verifyEnabled ? '✅' : '❌'}`);
    console.log(`- Update Rules: ${updateResult ? '✅' : '❌'}`);
    console.log(`- Disable: ${disableResult ? '✅' : '❌'}`);
    console.log(`- Verify Disable: ${!verifyDisabled ? '✅' : '❌'}`);
    
    return {
      enableResult,
      verifyEnabled,
      updateResult,
      disableResult,
      verifyDisabled
    };
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return { error: error.message };
  }
}

// Run the test
testDynamicPricing().then(results => {
  console.log('Test results:', results);
}).catch(error => {
  console.error('Test execution failed:', error);
});
