import React, { useEffect, useState, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { CheckCircle, Clock, Loader2, ChefHat, Truck, AlertCircle, MessageSquare, UtensilsCrossed } from 'lucide-react';
import Header from '@/components/Header';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { Order } from '@/types';
import { fetchOrderStatus, setupOrderStatusListener, getOrderStatusMessage } from '@/services/orderStatusService';
import { storeOrderInLocalHistory } from '@/services/orderHistoryService';
import { hasFeedbackBeenSubmitted } from '@/services/feedbackService';
import { OrderFeedbackForm } from '@/components/OrderFeedbackForm';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import StaffRequestButton from '@/components/StaffRequestButton';

// Status steps for the order tracker
const statusSteps = [
  { key: 'pending', label: 'Order Received', icon: CheckCircle },
  { key: 'preparing', label: 'Preparing', icon: ChefHat },
  { key: 'ready', label: 'Ready for Pickup', icon: Clock },
  { key: 'delivered', label: 'Delivered', icon: UtensilsCrossed },
  { key: 'completed', label: 'Completed', icon: CheckCircle },
];

// Map database status to our status steps
const mapStatusToStep = (status: string): number => {
  switch (status) {
    case 'pending':
      return 0;
    case 'preparing':
      return 1;
    case 'ready':
      return 2;
    case 'delivered':
      return 3;
    case 'completed':
      return 4;
    default:
      return 0;
  }
};

// Helper function to get a description for each status
const getStatusDescription = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'Your order has been received and is waiting for confirmation.';
    case 'preparing':
      return 'The kitchen is now preparing your delicious food!';
    case 'ready':
      return 'Your order is ready! Please pick it up at the counter.';
    case 'delivered':
      return 'Your food has been delivered to your table. Enjoy your meal!';
    case 'completed':
      return 'Your order has been completed. Thank you for dining with us!';
    default:
      return 'Order status updated.';
  }
};

const OrderStatus = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const [orderDetails, setOrderDetails] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [statusUpdated, setStatusUpdated] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  // Memoized fetch function to avoid dependency issues
  const fetchOrderStatusData = useCallback(async () => {
    if (!orderId) {
      setError('No order ID provided. Please go back to the order confirmation page.');
      setIsLoading(false);
      return;
    }

    try {
      const result = await fetchOrderStatus(orderId);
      if (result) {
        // Check if status has changed
        if (orderDetails && orderDetails.status !== result.status) {
          // Status has changed, show animation
          setStatusUpdated(true);

          // Reset animation after 3 seconds
          setTimeout(() => {
            setStatusUpdated(false);
          }, 3000);
          
          // Check if we need to show feedback request
          if (result.status.toLowerCase() === 'delivered' || result.status.toLowerCase() === 'completed') {
            // Check if feedback has already been submitted
            const feedbackCheck = await hasFeedbackBeenSubmitted(orderId);
            if (!feedbackCheck && !feedbackSubmitted) {
              // Show feedback dialog after a short delay to let the customer see the status change
              setTimeout(() => {
                setShowFeedback(true);
              }, 5000);
            }
          }
        }

        // Store the order in local history for future reference
        storeOrderInLocalHistory(result);
        
        setOrderDetails(result);
        setActiveStep(mapStatusToStep(result.status));
      } else {
        setError('Order not found. Please check your order ID and try again.');
      }
    } catch (err) {
      console.error('Error fetching order status:', err);
      setError('Failed to load order status. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, [orderId, orderDetails, feedbackSubmitted]);

  useEffect(() => {
    if (!orderId) return;

    // Initial fetch of order status
    fetchOrderStatusData();

    // Set up real-time subscription for order status changes
    const cleanup = setupOrderStatusListener(orderId, (updatedOrder) => {
      // Check if status has changed from what we have locally
      if (orderDetails && updatedOrder.status !== orderDetails.status) {
        // Show a toast notification with the new status
        toast.success(`Order status updated to: ${updatedOrder.status}`, {
          description: getOrderStatusMessage(updatedOrder.status),
          duration: 5000
        });
      }
      
      // Update the local state with new order data
      setOrderDetails(updatedOrder);
      setActiveStep(mapStatusToStep(updatedOrder.status));
      
      // Check if we should show feedback for delivered/completed orders
      if (['delivered', 'completed'].includes(updatedOrder.status.toLowerCase()) && !feedbackSubmitted) {
        hasFeedbackBeenSubmitted(orderId).then(hasSubmitted => {
          if (!hasSubmitted) {
            setTimeout(() => {
              setShowFeedback(true);
            }, 5000);
          }
        });
      }
    });

    // For backward compatibility, also set up polling as a fallback
    const interval = setInterval(fetchOrderStatusData, 30000);
    setRefreshInterval(interval);

    return () => {
      // Clean up subscription and interval when component unmounts
      cleanup();

      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [orderId, fetchOrderStatusData, orderDetails, feedbackSubmitted]);

  // Stop polling when order is completed
  useEffect(() => {
    if (orderDetails?.status && ['completed', 'cancelled'].includes(orderDetails.status.toLowerCase()) && refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }
  }, [orderDetails, refreshInterval]);

  const refreshStatus = () => {
    setIsLoading(true);
    fetchOrderStatus();
    toast.success('Order status refreshed');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col bg-restaurant-background">
        <Header title="Order Status" showBackButton={true} />
        <main className="flex-1 container mx-auto px-4 py-6 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <Loader2 className="h-12 w-12 text-restaurant-primary animate-spin mb-4" />
            <p className="text-restaurant-text text-lg">Loading order status...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex flex-col bg-restaurant-background">
        <Header title="Order Status" showBackButton={true} />
        <main className="flex-1 container mx-auto px-4 py-6 flex items-center justify-center">
          <Card className="w-full max-w-lg">
            <CardContent className="pt-6 flex flex-col items-center">
              <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
              <h2 className="text-xl font-bold text-restaurant-text mb-2">Error Loading Order</h2>
              <p className="text-restaurant-muted mb-6 text-center">{error}</p>
              <Link to="/menu">
                <Button className="w-full">Return to Menu</Button>
              </Link>
            </CardContent>
          </Card>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-restaurant-background">
      <Header title="Order Status" showBackButton={true} />

      <main className="flex-1 container mx-auto px-4 py-6">
        <div className="max-w-lg mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-restaurant-text">Order #{orderDetails?.orderNumber || '000'}</h1>
            <p className="text-restaurant-muted mt-2">
              Placed on {orderDetails?.createdAt ? new Date(orderDetails.createdAt).toLocaleString() : ''}
            </p>
          </div>

          {/* Order Status Tracker */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="font-semibold text-lg">Order Status</h2>
                <Button variant="outline" size="sm" onClick={refreshStatus}>
                  Refresh
                </Button>
              </div>

              <div className="relative">
                {/* Progress Bar */}
                <div className="absolute top-1/2 left-0 w-full h-1 bg-gray-200 -translate-y-1/2 z-0"></div>
                <div
                  className="absolute top-1/2 left-0 h-1 bg-restaurant-primary -translate-y-1/2 z-0 transition-all duration-500"
                  style={{ width: `${(activeStep / (statusSteps.length - 1)) * 100}%` }}
                ></div>

                {/* Status Steps */}
                <div className="relative z-10 flex justify-between">
                  {statusSteps.map((step, index) => {
                    const StepIcon = step.icon;
                    const isActive = index <= activeStep;
                    const isCurrent = index === activeStep;

                    return (
                      <div key={step.key} className="flex flex-col items-center">
                        <div
                          className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                            isActive
                              ? 'bg-restaurant-primary text-white'
                              : 'bg-gray-200 text-gray-500'
                          } ${
                            isCurrent ? 'ring-4 ring-restaurant-primary/30' : ''
                          } ${
                            isCurrent && statusUpdated ? 'animate-pulse' : ''
                          }`}
                        >
                          <StepIcon className={`h-5 w-5 ${isCurrent && statusUpdated ? 'animate-bounce' : ''}`} />
                        </div>
                        <span className={`text-xs font-medium text-center ${
                          isActive ? 'text-restaurant-text' : 'text-restaurant-muted'
                        }`}>
                          {step.label}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="mt-8 pt-4 border-t">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold">Estimated Time</h3>
                  <div className="flex items-center text-restaurant-accent">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{orderDetails?.estimatedTime || 15} mins</span>
                  </div>
                </div>
                <p className="text-sm text-restaurant-muted">
                  {activeStep === 0 && "Your order has been received and will be prepared soon."}
                  {activeStep === 1 && "Your order is being prepared by our kitchen staff."}
                  {activeStep === 2 && "Your order is ready for pickup!"}
                  {activeStep === 3 && "Your food has been delivered to your table. Enjoy your meal!"}
                  {activeStep === 4 && "Your order has been completed. Thank you for dining with us!"}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Order details card */}
          <Card className="mt-6">
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Order Details</h3>
              
              {orderDetails?.items && orderDetails.items.length > 0 ? (
                <div className="space-y-4">
                  {/* Order items */}
                  {orderDetails.items.map((item) => (
                    <div key={item.id} className="flex justify-between py-2 border-b last:border-0">
                      <div>
                        <div className="flex items-baseline">
                          <span className="font-medium mr-2">{item.quantity} ×</span>
                          <div>
                            <span className="font-medium">{item.name}</span>
                            <div className="text-sm text-restaurant-muted">
                              {item.quantity} × {item.name}
                            </div>
                          </div>
                        </div>
                        <div className="text-sm text-restaurant-muted">
                          €{item.price.toFixed(2)} each
                        </div>
                      </div>
                      <div className="font-medium">
                        €{(item.price * item.quantity).toFixed(2)}
                      </div>
                    </div>
                  ))}
                  
                  {/* Order total */}
                  <div className="border-t pt-4 flex justify-between font-semibold">
                    <span>Total</span>
                    <span>€{orderDetails?.total_amount.toFixed(2) || '0.00'}</span>
                  </div>
                </div>
              ) : (
                <p className="text-restaurant-muted">No items found in this order.</p>
              )}
            </CardContent>
          </Card>

          {/* Action buttons */}
          <div className="mt-6 space-y-4">
            <div className="flex space-x-4">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => navigate(`/menu?table=${orderDetails?.table_id}`)}
              >
                Order More
              </Button>

              {/* Only show feedback button for completed/delivered orders */}
              {orderDetails && ['delivered', 'completed'].includes(orderDetails.status.toLowerCase()) && (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setShowFeedback(true)}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Share Feedback
                </Button>
              )}

              <Button
                className="w-full bg-restaurant-primary hover:bg-restaurant-primary/90"
                onClick={() => navigate(-1)}
              >
                Go Back
              </Button>
            </div>

            {/* Staff Request Button */}
            <div className="flex justify-center">
              <StaffRequestButton
                tableId={orderDetails?.table_id}
                restaurantId={orderDetails?.restaurant_id || ''}
                className="w-full max-w-xs"
              />
            </div>
          </div>
          
          {/* Feedback dialog */}
          <Dialog open={showFeedback} onOpenChange={setShowFeedback}>
            <DialogContent className="sm:max-w-md">
              <OrderFeedbackForm
                orderId={orderId || ''} 
                restaurantId={orderDetails?.restaurant_id || ''}
                tableId={orderDetails?.table_id}
                onCompleted={() => {
                  setShowFeedback(false);
                  setFeedbackSubmitted(true);
                  toast.success('Thank you for your feedback!');
                }}
                onDismiss={() => {
                  setShowFeedback(false);
                  setFeedbackSubmitted(true);
                }}
              />
            </DialogContent>
          </Dialog>
        </div>
      </main>

      <footer className="bg-restaurant-background/80 border-t border-restaurant-primary/10 py-6">
        <div className="container mx-auto px-4 text-center text-sm text-restaurant-muted">
          <p> {new Date().getFullYear()} MenuFlow - A subsidiary of SME Analytica</p>
          <p className="mt-2">Powered by <span className="text-restaurant-secondary">SME Analytica</span></p>
        </div>
      </footer>
    </div>
  );
};

export default OrderStatus;
