import { supabase } from '@/integrations/supabase/client';
import { getRestaurantId } from './restaurantDbService';
import { getCurrentTraffic } from './trafficService';
import { User } from '@supabase/supabase-js';
import { pricingLogger } from '@/utils/debugLogger';

// Helper debug logging function
const debugLogger = (category: string, message: string, data?: unknown) => {
  console.log(`[${category}] ${message}`, data || '');
  // This can be expanded to use a more sophisticated logging system in the future
};

interface DynamicPricingParams {
  restaurantId: string;
  currentTraffic?: number; // 0-1 value representing current traffic level
  timeOfDay?: string;
  dayOfWeek?: number;
}

interface DynamicPricingResult {
  priceFactor: number;
  insights: string[];
  nextUpdateTime: string;
  confidence: number;
}

interface MenuItem {
  id: string;
  name: string;
  category?: string;
  price?: number;
  base_price?: number;
  description?: string;
  image?: string;
  allergies?: string[];
  calories?: number;
  price_factor?: number;
  isDynamicallyPriced?: boolean;
  // Allow other properties with string keys and various types
  [key: string]: string | number | boolean | string[] | undefined;
}

/**
 * Get dynamic pricing factor from the AI pricing engine based on traffic level and custom rules
 */
async function getDynamicPricingFactor(restaurantId: string, trafficLevel: number, customRules: PricingRules): Promise<number> {
  console.log('⚡ getDynamicPricingFactor called with params:', { restaurantId, trafficLevel, customRules });
  console.log('🕒 Current time:', new Date().toLocaleTimeString());

  try {
    // TEMPORARY: Use mock data instead of actual API call for testing
    console.log('🔍 Using mock AI pricing data for testing');

    // Calculate price factor based on current traffic
    // Get traffic estimate from restaurant tables
    // const trafficLevel = await getCurrentTraffic(params.restaurantId);

    // Get custom pricing rules for this restaurant
    // const customRules = await getPricingRules(params.restaurantId);

    // Log detailed information about the traffic and pricing rules
    pricingLogger.log('Traffic and pricing rules data', {
      trafficLevel,
      customRules,
      applyToCategories: customRules.applyToCategories,
      isCustomerView: false,
      timestamp: new Date().toISOString()
    });

    // Handle null traffic level (when no data is available)
    if (trafficLevel === null) {
      console.warn('⚠️ No traffic data available — skipping dynamic pricing');
      return 1.0; // Default price factor when no traffic data is available
    }

    console.log('📈 Estimated traffic:', trafficLevel, `(${(trafficLevel * 100).toFixed(1)}% occupancy)`);
    console.log('📈 Traffic source:', 'Provided in params (simulated)');

    let priceFactor = 1.0;
    let trafficCategory = 'normal';
    let insightMessage = ''; // Initialize insight message

    // Enhanced debug info for traffic level and pricing
    console.log(`📈 TRAFFIC LEVEL ANALYSIS`);
    console.log(`📈 Current traffic: ${(trafficLevel * 100).toFixed(1)}%`);
    console.log(`📈 High threshold: ${(customRules?.highTraffic?.threshold || 0.8) * 100}%`);
    console.log(`📈 Medium threshold: ${(customRules?.mediumTraffic?.threshold || 0.5) * 100}%`);
    console.log(`📈 Low threshold: ${(customRules?.lowTraffic?.threshold || 0.2) * 100}%`);

    // Apply rules based on traffic thresholds
    if (trafficLevel >= (customRules?.highTraffic?.threshold || 0.8)) {
      // High traffic - ALWAYS INCREASE prices
      const percentage = Math.abs(customRules?.highTraffic?.percentage || 10);
      priceFactor = 1 + (percentage / 100);
      insightMessage = `High traffic detected (${Math.round(trafficLevel * 100)}%). Prices increased by ${percentage}%.`;
      trafficCategory = 'high';

      pricingLogger.log('Using HIGH traffic pricing rule', {
        trafficLevel,
        threshold: customRules?.highTraffic?.threshold || 0.8,
        percentage,
        resultingFactor: priceFactor
      });

      console.log(`🔴 HIGH TRAFFIC (${Math.round(trafficLevel * 100)}%) - Dynamic pricing applied!`);
      console.log(`   Adjustment: +${percentage}%, Price factor: ${priceFactor.toFixed(2)}`);

    } else if (trafficLevel >= (customRules?.mediumTraffic?.threshold || 0.5)) {
      // Medium traffic - slight increases
      const percentage = Math.abs(customRules?.mediumTraffic?.percentage || 5);
      priceFactor = 1 + (percentage / 100);
      insightMessage = `Medium traffic detected (${Math.round(trafficLevel * 100)}%). Prices increased by ${percentage}%.`;
      trafficCategory = 'medium';

      pricingLogger.log('Using MEDIUM traffic pricing rule', {
        trafficLevel,
        threshold: customRules?.mediumTraffic?.threshold || 0.5,
        percentage,
        resultingFactor: priceFactor
      });

      console.log(`🟠 MEDIUM TRAFFIC (${Math.round(trafficLevel * 100)}%) - Dynamic pricing applied!`);
      console.log(`   Adjustment: +${percentage}%, Price factor: ${priceFactor.toFixed(2)}`);

    } else if (trafficLevel <= (customRules?.lowTraffic?.threshold || 0.2)) {
      // Low traffic - consider a discount
      const percentage = Math.abs(customRules?.lowTraffic?.percentage || 5);
      trafficCategory = 'low';

      // Check if we should decrease (negative percentage) or increase prices
      if ((customRules?.lowTraffic?.percentage || 0) < 0) {
        priceFactor = 1 - (percentage / 100); // Discount
        insightMessage = `Low traffic detected (${Math.round(trafficLevel * 100)}%). Prices decreased by ${percentage}%.`;
        console.log(`🟢 LOW TRAFFIC (${Math.round(trafficLevel * 100)}%) - Applied DISCOUNT`);
        console.log(`   Adjustment: -${percentage}%, Price factor: ${priceFactor.toFixed(2)}`);
      } else {
        priceFactor = 1 + (percentage / 100); // Increase
        insightMessage = `Low traffic detected (${Math.round(trafficLevel * 100)}%). Prices increased by ${percentage}%.`;
        console.log(`🟢 LOW TRAFFIC (${Math.round(trafficLevel * 100)}%) - Applied INCREASE`);
        console.log(`   Adjustment: +${percentage}%, Price factor: ${priceFactor.toFixed(2)}`);
      }

      pricingLogger.log('Using LOW traffic pricing rule', {
        trafficLevel,
        threshold: customRules?.lowTraffic?.threshold || 0.2,
        percentage,
        resultingFactor: priceFactor,
        isDiscount: (customRules?.lowTraffic?.percentage || 0) < 0
      });
    } else {
      // Normal traffic - no adjustment
      insightMessage = `Normal traffic detected (${Math.round(trafficLevel * 100)}%). No price adjustment.`;
      trafficCategory = 'normal';

      pricingLogger.log('Using NORMAL traffic pricing (no adjustment)', {
        trafficLevel,
        resultingFactor: priceFactor
      });

      console.log(`🟡 NORMAL TRAFFIC (${Math.round(trafficLevel * 100)}%) - No price adjustment`);
      console.log(`   Price factor: ${priceFactor.toFixed(2)}`);
    }

    // Safety check - ENSURE high traffic always has price factor > 1.0
    if (trafficLevel >= 0.7 && priceFactor <= 1.0) {
      console.log(`⚠️ Traffic level (${(trafficLevel * 100).toFixed(1)}%) indicates high traffic but price factor is ${priceFactor.toFixed(2)} - FIXING!`);
      priceFactor = Math.max(priceFactor, 1.1); // Minimum 10% increase for high traffic
      console.log(`🔧 Corrected price factor to ${priceFactor.toFixed(2)}`);
    }

    // Get AI confidence score (using default or threshold from rules)
    const confidenceScore = customRules?.confidenceThreshold || 0.7;

    // Mock response with appropriate price factor
    const data = {
      price_factor: priceFactor,
      insights: [
        `Current restaurant traffic: ${Math.round(trafficLevel * 100)}%`,
        `Traffic category: ${trafficCategory}`,
        `Time of day: ${getCurrentTimeOfDay()}`, // Now using the exported function
        insightMessage,
        `Recommended price adjustment: ${((priceFactor - 1) * 100).toFixed(1)}%`
      ],
      next_update: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
      confidence_score: confidenceScore
    };
  console.log('🔥 PRODUCTION applyToCategories:', customRules.applyToCategories);

    // Return just the price factor instead of the full result object
    return data.price_factor || 1.0;
  } catch (error) {
    console.error('Error fetching dynamic pricing factor:', error);
    console.warn('getDynamicPricingFactor failed—using defaults:', error);
    // Fallback to a default 10% increase during typical peak hours
    const isPeak = isTypicalPeakHour();
    return isPeak ? 1.1 : 1.0;
  }
};

/**
 * Apply dynamic pricing to menu items using AI-determined pricing factor
 */
// Cache for storing dynamic pricing results
const dynamicPricingCache: {
  [restaurantId: string]: {
    items: MenuItem[];
    timestamp: number;
    priceFactor: number;
  };
} = {};

// Function to check if cache is still valid (within 5 minutes)
const isValidCache = (restaurantId: string): boolean => {
  const cache = dynamicPricingCache[restaurantId];
  if (!cache) return false;

  // Cache valid for 5 minutes (300000ms)
  return (Date.now() - cache.timestamp) < 300000;
};

export async function applyDynamicPricing(
  menuItems: MenuItem[],
  restaurantId: string,
  isCustomerView: boolean = false
): Promise<MenuItem[]> {
  try {
  // Start detailed logging specifically for this function
  pricingLogger.section(`applyDynamicPricing (${isCustomerView ? 'CUSTOMER_VIEW' : 'ADMIN_VIEW'})`);
  pricingLogger.log('Function called', {
    restaurantId,
    isCustomerView,
    itemCount: menuItems.length,
    timestamp: new Date().toISOString(),
    url: typeof window !== 'undefined' ? window.location.href : 'unknown'
  });
  
  // Check if dynamic pricing is enabled for this restaurant
  const isPricingEnabled = await isDynamicPricingEnabled(restaurantId);
  const timestamp = new Date().toISOString();
  
  console.log(`🔐 Dynamic pricing enabled (${isCustomerView ? 'CUSTOMER_VIEW' : 'ADMIN_VIEW'}):`, isPricingEnabled);
  const currentUrl = typeof window !== 'undefined' ? window.location.href : 'unknown';
  debugLogger('dynamic-pricing', 'Dynamic pricing enabled check result', { isPricingEnabled, restaurantId, isCustomerView, timestamp, url: currentUrl });
  
  if (!isPricingEnabled) {
    return menuItems;
  }

  console.log(`🍔 Applying dynamic pricing for restaurant: ${restaurantId}`);
  console.log(`📋 Number of menu items to process: ${menuItems.length}`);
  
  // We'll disable the cache for now
  const useSessionCache = false;
  
  // If we're disabling the cache for debugging
  if (!useSessionCache) {
    console.log('💡 Fresh calculation forced - cache temporarily disabled');
  }
  
  // Force refresh pricing rules from database on each invocation
  // This ensures we always use the latest database settings, not cached data
  const customRules = await getPricingRules(restaurantId, null, true);
  console.log('applyDynamicPricing - FRESH DB rules:', customRules.applyToCategories);

  // Get the current traffic level for this restaurant
  // This comes from TrafficService
  const trafficLevel = await getCurrentTraffic(restaurantId);

  debugLogger('dynamic-pricing', 'Traffic and pricing rules data', {
    trafficLevel, 
    customRules,
    isCustomerView: false,
    timestamp: new Date().toISOString()
  });

  // Determine which categories to price based on database settings
  const applyToFood = customRules.applyToCategories?.food === true;
  const applyToDrinks = customRules.applyToCategories?.drinks === true;
  console.log(`Category toggles - food: ${applyToFood}, drinks: ${applyToDrinks}`);

  // Calculate dynamic pricing factor based on traffic and rules
  const priceFactor = await getDynamicPricingFactor(restaurantId, trafficLevel, customRules);
  console.log('applyDynamicPricing - priceFactor:', priceFactor);

    const priceChangesCount = 0;
    const totalPriceChangePercent = 0;

    const modifiedItems = menuItems.map(item => {
      // Deep copy the item to avoid modifying the original
      const modifiedItem = { ...item };

      // Store the original price as base_price if not already set
      if (!modifiedItem.base_price && modifiedItem.price) {
        modifiedItem.base_price = modifiedItem.price;
      }

      // Skip items without a base price
      if (!modifiedItem.base_price) {
        pricingLogger.log('Skipping item with no base price', {
          itemId: modifiedItem.id,
          itemName: modifiedItem.name
        });
        return modifiedItem;
      }

      // Determine if we should apply pricing to this item based on its category
      const category = (modifiedItem.category || '').toLowerCase().trim();

      // Enhanced drink detection - check both exact matches and includes
      const drinkCategories = [
        'drink', 'drinks', 'beverage', 'beverages', 'bebida', 'bebidas',
        'cocktail', 'cocktails', 'wine', 'beer', 'soda', 'juice',
        'coffee', 'tea', 'water', 'soft drink', 'alcohol', 'bar', 'beverage'
      ];

      // Check if the category is exactly a drink or contains a drink word
      const isDrink = drinkCategories.some(drinkCat =>
        category === drinkCat || category.includes(drinkCat)
      );

      // Force drink if category is exactly 'drinks' or contains 'beverage'
      const forceDrink = category === 'drinks' || category.includes('beverage');

      // If it's not a drink, consider it food
      const isFood = !isDrink;

      // Log category detection for debugging
      console.log(`Item: ${modifiedItem.name} | Category: ${category} | isDrink: ${isDrink} | isFood: ${isFood} | forceDrink: ${forceDrink}`);
      console.log(`Category settings from rules: applyToFood=${applyToFood}, applyToDrinks=${applyToDrinks}`);

      // IMPORTANT: Handle forceDrink first, then check isDrink vs isFood
      let shouldApplyPricing = false;
      
      // CRITICAL FIX: Ensure we're using the correct boolean values for category settings
      // Use strict equality to prevent type coercion issues
      const enableDrinksPricing = applyToDrinks === true;
      const enableFoodPricing = applyToFood === true;
      
      console.log(`🔄 ITEM CATEGORY CHECK for ${modifiedItem.name}: drinks setting=${enableDrinksPricing}, food setting=${enableFoodPricing}`);

      if (forceDrink || isDrink) {
        // For drink items (forced or detected), use the drink setting
        shouldApplyPricing = enableDrinksPricing;
        console.log(`🍹 Drink item '${modifiedItem.name}' - ${shouldApplyPricing ? 'APPLYING' : 'SKIPPING'} pricing (drinks enabled: ${enableDrinksPricing})`);
      } else if (isFood) {
        // For food items, use the food setting
        shouldApplyPricing = enableFoodPricing;
        console.log(`🍽️ Food item '${modifiedItem.name}' - ${shouldApplyPricing ? 'APPLYING' : 'SKIPPING'} pricing (food enabled: ${enableFoodPricing})`);
      } else {
        // For items that are neither explicitly food nor drink, default to food setting
        shouldApplyPricing = enableFoodPricing;
        console.log(`❓ Uncategorized item '${modifiedItem.name}' - defaulting to food setting: ${enableFoodPricing}`);
      }

      // Add detailed debug information
      console.log(`🔍 PRICING DECISION for ${modifiedItem.name}:`);
      console.log(`  - Category: "${category}"`);
      console.log(`  - Is drink item? ${isDrink}`);
      console.log(`  - Is food item? ${isFood}`);
      console.log(`  - Force drink? ${forceDrink}`);
      console.log(`  - Apply to food items? ${enableFoodPricing}`);
      console.log(`  - Apply to drink items? ${enableDrinksPricing}`);
      console.log(`  - Final decision: ${shouldApplyPricing ? 'APPLY PRICING' : 'DO NOT APPLY PRICING'}`);

      console.log(`Should apply pricing to ${modifiedItem.name}? ${shouldApplyPricing} (isDrink: ${isDrink}, enableDrinksPricing: ${enableDrinksPricing})`);

      // Apply price adjustments when enabled
      if (shouldApplyPricing && modifiedItem.base_price != null) {
        const adjustedPrice = parseFloat((modifiedItem.base_price * priceFactor).toFixed(2));
        modifiedItem.price = adjustedPrice;
        modifiedItem.price_factor = priceFactor;
        modifiedItem.isDynamicallyPriced = true;
      } else {
        modifiedItem.isDynamicallyPriced = false;
      }

      return modifiedItem;
    });
    return modifiedItems;
  } catch (error) {
    console.error('Error applying dynamic pricing:', error);
    return menuItems;
  }
}

/**
 * Activate or deactivate dynamic pricing for a restaurant
 */
export const setDynamicPricingEnabled = async (
  enabled: boolean,
  restaurantId?: string,
  user?: User | null
): Promise<boolean> => {
  try {
    // Get restaurant ID if not provided
    let targetRestaurantId = restaurantId;
    
    if (!targetRestaurantId) {
      // Check if this is a customer view
      const isCustomerView = typeof window !== 'undefined' && (
        window.location.pathname.includes('/menu') ||
        window.location.pathname.includes('/table')
      );
      
      if (isCustomerView) {
        // For customer views, try to get restaurant ID from URL or localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const tableId = urlParams.get('table');
        
        if (tableId) {
          // Get restaurant ID from table ID
          try {
            const { data, error } = await supabase
              .from('restaurant_tables')
              .select('restaurant_id')
              .eq('id', tableId)
              .single();
              
            if (!error && data?.restaurant_id) {
              targetRestaurantId = data.restaurant_id;
            }
          } catch (error) {
            console.warn('Error getting restaurant ID from table ID in setDynamicPricingEnabled:', error);
          }
        }
        
        // If still no restaurant ID, try localStorage
        if (!targetRestaurantId) {
          targetRestaurantId = localStorage.getItem('selectedRestaurantId');
        }
      } else {
        // For admin views, use the authenticated user
        targetRestaurantId = await getRestaurantId(user);
      }
    }

    if (!targetRestaurantId) {
      console.error('No restaurant ID found for dynamic pricing update');
      return false;
    }

    console.log(`💾 Updating dynamic pricing in database for restaurant ${targetRestaurantId} to ${enabled ? 'ENABLED' : 'DISABLED'}`);

    // Check if the business exists in the new architecture
    const { data: existingBusiness, error: checkError } = await supabase
      .from('businesses')
      .select('id')
      .eq('id', targetRestaurantId)
      .single();

    if (checkError) {
      console.error('Error checking if business exists:', checkError);
      return false;
    }

    let updateResult: { error?: unknown };

    if (existingBusiness) {
      console.log('Business found, updating dynamic pricing setting in restaurant_details');

      // First check if restaurant_details entry exists
      const { data: existingDetails, error: detailsCheckError } = await supabase
        .from('restaurant_details')
        .select('id')
        .eq('business_id', targetRestaurantId)
        .single();

      if (detailsCheckError && detailsCheckError.code !== 'PGRST116') {
        console.error('Error checking restaurant details:', detailsCheckError);
        return false;
      }

      if (existingDetails) {
        // Update existing restaurant details
        updateResult = await supabase
          .from('restaurant_details')
          .update({
            dynamic_pricing_enabled: enabled,
            updated_at: new Date().toISOString()
          })
          .eq('business_id', targetRestaurantId);
      } else {
        // Create new restaurant details entry
        console.log('Creating new restaurant_details entry');
        updateResult = await supabase
          .from('restaurant_details')
          .insert({
            business_id: targetRestaurantId,
            dynamic_pricing_enabled: enabled,
            pricing_rules: {
              highTraffic: { threshold: 0.8, percentage: 10 },
              mediumTraffic: { threshold: 0.5, percentage: 5 },
              lowTraffic: { threshold: 0.3, percentage: -5 },
              applyToCategories: { food: true, drinks: false },
              confidenceThreshold: 0.7
            },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
      }
    } else {
      console.error('Business not found in database:', targetRestaurantId);
      return false;
    }

    if (updateResult.error) {
      console.error('Error updating/inserting dynamic pricing settings:', updateResult.error);

      // Fallback to localStorage if database update fails
      console.log('⚠️ Database update failed, falling back to localStorage');
      const storageKey = `dynamic_pricing_${targetRestaurantId}`;
      localStorage.setItem(storageKey, enabled ? 'true' : 'false');
      localStorage.setItem(`${storageKey}_timestamp`, Date.now().toString()); // Add timestamp

      return false;
    }

    // Clear any localStorage overrides to ensure we use the database value
    const explicitDisableKey = `dynamic_pricing_disabled_${targetRestaurantId}`;
    if (localStorage.getItem(explicitDisableKey)) {
      localStorage.removeItem(explicitDisableKey);
    }

    // Update localStorage for faster access but with TTL
    const storageKey = `dynamic_pricing_${targetRestaurantId}`;
    localStorage.setItem(storageKey, enabled ? 'true' : 'false');
    localStorage.setItem(`${storageKey}_timestamp`, Date.now().toString());

    // Clear any cached simulation data that might affect pricing
    const mockTrafficKey = 'simulatedTrafficData';
    if (sessionStorage.getItem(mockTrafficKey)) {
      sessionStorage.removeItem(mockTrafficKey);
    }

    // Trigger a custom event for other components to react to the change
    try {
      window.dispatchEvent(new CustomEvent('dynamic-pricing-changed', {
        detail: { restaurantId: targetRestaurantId, enabled }
      }));
    } catch (e) {
      // Ignore event dispatch errors
    }

    console.log(`✅ Dynamic pricing for restaurant ${targetRestaurantId} set to ${enabled ? 'ENABLED' : 'DISABLED'} in database`);
    return true;
  } catch (error) {
    console.error('Error setting dynamic pricing:', error);
    return false;
  }
};

/**
 * Check if dynamic pricing is enabled for a restaurant
 */
export const isDynamicPricingEnabled = async (
  restaurantId?: string,
  user?: User | null
): Promise<boolean> => {
  try {
    // Add context info for debugging
    const isCustomerView = typeof window !== 'undefined' && (
      window.location.pathname.includes('/menu') ||
      window.location.pathname.includes('/table')
    );
    const viewType = isCustomerView ? 'CUSTOMER_VIEW' : 'ADMIN_VIEW';

    pricingLogger.section(`isDynamicPricingEnabled check (${viewType})`);
    pricingLogger.log('Function called with params', { restaurantId, userProvided: !!user });

    // Get restaurant ID if not provided
    let targetRestaurantId = restaurantId;
    
    if (!targetRestaurantId) {
      // For customer views, try to get restaurant ID from URL or localStorage
      if (isCustomerView) {
        // Try to get from URL parameters first
        const urlParams = new URLSearchParams(window.location.search);
        const tableId = urlParams.get('table');
        
        if (tableId) {
          // Get restaurant ID from table ID
          try {
            const { data, error } = await supabase
              .from('restaurant_tables')
              .select('restaurant_id')
              .eq('id', tableId)
              .single();
              
            if (!error && data?.restaurant_id) {
              targetRestaurantId = data.restaurant_id;
              console.log('🔍 Got restaurant ID from table ID:', targetRestaurantId);
            }
          } catch (error) {
            console.warn('Error getting restaurant ID from table ID:', error);
          }
        }
        
        // If still no restaurant ID, try localStorage
        if (!targetRestaurantId) {
          targetRestaurantId = localStorage.getItem('selectedRestaurantId');
          if (targetRestaurantId) {
            console.log('🔍 Got restaurant ID from localStorage:', targetRestaurantId);
          }
        }
      } else {
        // For admin views, use the authenticated user
        targetRestaurantId = await getRestaurantId(user);
      }
    }

    if (!targetRestaurantId) {
      console.error('No restaurant ID found for dynamic pricing check');
      pricingLogger.error('No restaurant ID found for dynamic pricing check');
      return false;
    }

    console.log('🔍 Checking dynamic pricing enabled status');
    console.log('🏪 Restaurant ID:', targetRestaurantId);
    pricingLogger.log('Checking dynamic pricing for restaurant', {
      restaurantId: targetRestaurantId,
      viewType,
      path: typeof window !== 'undefined' ? window.location.pathname : 'unknown'
    });

    // Check for explicit disable in localStorage (highest priority, overrides all other settings)
    const explicitDisableKey = `dynamic_pricing_disabled_${targetRestaurantId}`;
    if (localStorage.getItem(explicitDisableKey) === 'true') {
      console.log('%c🚫 DYNAMIC PRICING EXPLICITLY DISABLED FOR THIS RESTAURANT', 'background: #FF0000; color: white; font-weight: bold; padding: 2px');
      pricingLogger.log('Dynamic pricing explicitly disabled via localStorage override');
      return false;
    }


    // We will first try to get the database value and only use localStorage as a fallback
    // This ensures we always have the latest setting directly from the database
    console.log('🔍 Checking dynamic pricing in database (primary source)');

    // Finally try to get the setting from the database
    console.log('🔍 Checking dynamic pricing in database');

    try {
      // Define the type for our restaurant data
      interface RestaurantSettings {
        dynamic_pricing_enabled?: boolean;
      }

      // Try the RPC function first (if it exists)
      let data, error;
      try {
        const rpcResult = await supabase
          .rpc('get_dynamic_pricing_settings', { restaurant_id_param: targetRestaurantId });
        data = rpcResult.data;
        error = rpcResult.error;
      } catch (rpcError) {
        console.warn('⚠️ RPC function not available, falling back to direct query:', rpcError);
        // Fall back to direct query using the new architecture
        const directResult = await supabase
          .from('restaurant_details')
          .select('dynamic_pricing_enabled')
          .eq('business_id', targetRestaurantId)
          .single();

        if (directResult.error && directResult.error.code !== 'PGRST116') {
          error = directResult.error;
        } else if (directResult.data) {
          data = [{ dynamic_pricing_enabled: directResult.data.dynamic_pricing_enabled }];
        } else {
          // No restaurant_details found, default to false
          data = [{ dynamic_pricing_enabled: false }];
        }
      }

      if (error) {
        console.warn('⚠️ Error checking dynamic pricing in database:', error);
        // Continue to fallbacks
      } else if (data && data.length > 0) {
        // We found valid data in the database
        const enabled = Boolean(data[0].dynamic_pricing_enabled);
        console.log(`💡 Dynamic pricing ${enabled ? 'ENABLED' : 'DISABLED'} from database`);
        // Cache this value in localStorage for future use, but with short TTL (24 hours)
        const storageKey = `dynamic_pricing_${targetRestaurantId}`;
        localStorage.setItem(storageKey, enabled.toString());
        localStorage.setItem(`${storageKey}_timestamp`, Date.now().toString());
        return enabled;
      } else {
        console.log('⚠️ No data returned from database for restaurant ID:', targetRestaurantId);
      }

      // If we get here, either there was an error or no data was found
      // Check localStorage as a fallback
      console.log('🔍 Checking localStorage as fallback');
      const storageKey = `dynamic_pricing_${targetRestaurantId}`;
      const storedValue = localStorage.getItem(storageKey);
      console.log('🔑 Storage key:', storageKey);
      console.log('📝 Stored value:', storedValue);

      if (storedValue !== null) {
        const enabled = storedValue === 'true';
        console.log(`💡 Dynamic pricing ${enabled ? 'ENABLED' : 'DISABLED'} from localStorage fallback`);
        return enabled;
      }

      // If all checks fail, default to DISABLED for both customer and admin views
      // This ensures that dynamic pricing is only enabled when explicitly set by the restaurant owner
      console.log('⚠️ All checks failed - defaulting dynamic pricing to DISABLED (respecting restaurant owner settings)');
      return false;
    } catch (dbError) {
      console.error('🚨 Unexpected error in database check:', dbError);
      // Default to disabled for API errors to respect restaurant owner settings
      return false;
    }

    // We should never reach here due to the returns above, but just in case
    console.log('🚨 No dynamic pricing setting found - defaulting to DISABLED');
    return false; // This is unreachable but kept for safety
  } catch (error) {
    console.error('Error checking if dynamic pricing is enabled:', error);
    return false;
  }
};

/**
 * Get cached dynamic pricing data for display purposes
 */
export const getCachedDynamicPricingData = (): DynamicPricingResult | null => {
  try {
    const cachedData = sessionStorage.getItem('dynamicPricingData');
    return cachedData ? JSON.parse(cachedData) : null;
  } catch (error) {
    console.error('Error retrieving cached pricing data:', error);
    return null;
  }
};

/**
 * Force a refresh of pricing rules from the database
 * This is useful when the UI has been updated but the changes aren't reflected in the app
 */
export const forcePricingRulesRefresh = async (restaurantId: string): Promise<PricingRules> => {
  console.log('🔄 Forcing refresh of pricing rules from database for restaurant:', restaurantId);

  // Clear localStorage cache first
  clearPricingRulesCache(restaurantId);

  // Then get fresh data from the database
  return getPricingRules(restaurantId, null, true);
};

/**
 * Update pricing rules in the database and clear all caches
 * This is useful when the UI settings need to be applied immediately
 */
export const updatePricingRules = async (
  restaurantId: string,
  rules: PricingRules
): Promise<boolean> => {
  console.log('🔄 Updating pricing rules in database for restaurant:', restaurantId);
  console.log('🔄 Rules from UI:', rules);

  try {
    // Ensure applyToCategories exists with the correct structure
    if (!rules.applyToCategories) {
      // Use default values if not provided
      rules.applyToCategories = {
        food: true,  // Food is enabled by default
        drinks: false // Drinks are disabled by default
      };
    } else {
      // CRITICAL: Ensure the category settings are strictly boolean values
      // This is essential for proper database storage and retrieval
      const foodSetting = rules.applyToCategories.food === true;
      const drinksSetting = rules.applyToCategories.drinks === true;
      
      rules.applyToCategories = {
        food: foodSetting,
        drinks: drinksSetting
      };
      
      console.log('🔄 SAVING EXACT CATEGORY SETTINGS:', {
        "food (boolean)": foodSetting,
        "food (typeof)": typeof foodSetting,
        "drinks (boolean)": drinksSetting,
        "drinks (typeof)": typeof drinksSetting
      });
    }

    // Add timestamp
    rules.updatedAt = new Date().toISOString();

    // Update the database using the new architecture
    // First check if restaurant_details entry exists
    const { data: existingDetails, error: detailsCheckError } = await supabase
      .from('restaurant_details')
      .select('id')
      .eq('business_id', restaurantId)
      .single();

    let updateError;
    if (existingDetails) {
      // Update existing restaurant details
      const { error } = await supabase
        .from('restaurant_details')
        .update({
          pricing_rules: rules,
          updated_at: new Date().toISOString()
        })
        .eq('business_id', restaurantId);
      updateError = error;
    } else {
      // Create new restaurant details entry
      console.log('Creating new restaurant_details entry for pricing rules');
      const { error } = await supabase
        .from('restaurant_details')
        .insert({
          business_id: restaurantId,
          pricing_rules: rules,
          dynamic_pricing_enabled: false, // Default to disabled
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      updateError = error;
    }

    if (updateError) {
      console.error('Error updating pricing rules in database:', updateError);
      return false;
    }

    // Clear all caches to ensure fresh data is loaded
    clearPricingRulesCache(restaurantId);

    // Update localStorage with the new rules
    const storageKey = `pricing_rules_${restaurantId}`;
    localStorage.setItem(storageKey, JSON.stringify(rules));

    console.log('✅ Pricing rules updated successfully');
    return true;
  } catch (error) {
    console.error('Error updating pricing rules:', error);
    return false;
  }
};

/**
 * Enable or disable dynamic pricing for a specific category (food or drinks)
 */
export const setCategoryDynamicPricing = async (
  restaurantId: string,
  category: 'food' | 'drinks',
  enabled: boolean
): Promise<boolean> => {
  try {
    // Refresh current rules from database
    const currentRules = await getPricingRules(restaurantId, null, true);
    // Update only the specified category toggle
    const updatedRules = {
      ...currentRules,
      applyToCategories: {
        ...currentRules.applyToCategories,
        [category]: enabled,
      },
    };
    // Persist the updated rules
    const success = await updatePricingRules(restaurantId, updatedRules);
    if (!success) {
      console.error(`Failed to update pricing rules for category '${category}'`);
    }
    return success;
  } catch (error) {
    console.error(`Error toggling dynamic pricing for ${category}:`, error);
    return false;
  }
}

/**
 * Clear the localStorage cache for pricing rules
 * This is useful when you want to force a refresh from the database
 */
export const clearPricingRulesCache = (restaurantId: string): void => {
  console.log('🧹 Clearing pricing rules cache for restaurant:', restaurantId);

  // Clear the pricing rules cache
  const storageKey = `pricing_rules_${restaurantId}`;
  localStorage.removeItem(storageKey);

  // Also clear any related caches
  const dynamicPricingKey = `dynamic_pricing_${restaurantId}`;
  localStorage.removeItem(dynamicPricingKey);
  localStorage.removeItem(`${dynamicPricingKey}_timestamp`);

  // Clear session storage caches
  const cacheKey = `dynamicPricing_${restaurantId}`;
  sessionStorage.removeItem(cacheKey);
  sessionStorage.removeItem('dynamicPricingData');

  console.log('✅ Pricing rules cache cleared');
};

/**
 * Interface for custom pricing rules
 */
export interface PricingRules {
  highTraffic?: {
    percentage: number;
    threshold: number;
  };
  mediumTraffic?: {
    percentage: number;
    threshold: number;
  };
  lowTraffic?: {
    percentage: number;
    threshold: number;
  };
  applyToCategories?: {
    food: boolean;
    drinks: boolean;
  };
  confidenceThreshold?: number;
  updatedAt?: string;
}

/**
 * Get custom pricing rules for a restaurant
 */
export const getPricingRules = async (restaurantId?: string, user?: User | null, forceDbRefresh: boolean = false): Promise<PricingRules> => {
  try {
    // If restaurantId is not provided, try to get it from the user or URL/localStorage
    let targetRestaurantId = restaurantId;
    
    if (!targetRestaurantId) {
      // Check if this is a customer view
      const isCustomerView = typeof window !== 'undefined' && (
        window.location.pathname.includes('/menu') ||
        window.location.pathname.includes('/table')
      );
      
      if (isCustomerView) {
        // For customer views, try to get restaurant ID from URL or localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const tableId = urlParams.get('table');
        
        if (tableId) {
          // Get restaurant ID from table ID
          try {
            const { data, error } = await supabase
              .from('restaurant_tables')
              .select('restaurant_id')
              .eq('id', tableId)
              .single();
              
            if (!error && data?.restaurant_id) {
              targetRestaurantId = data.restaurant_id;
            }
          } catch (error) {
            console.warn('Error getting restaurant ID from table ID in getPricingRules:', error);
          }
        }
        
        // If still no restaurant ID, try localStorage
        if (!targetRestaurantId) {
          targetRestaurantId = localStorage.getItem('selectedRestaurantId');
        }
      } else {
        // For admin views, use the authenticated user
        targetRestaurantId = await getRestaurantId(user);
      }
    }

    console.log('🔍 Getting custom pricing rules for restaurant:', targetRestaurantId);

    if (!targetRestaurantId) {
      console.error('No restaurant ID found for getting pricing rules');
      return getDefaultPricingRules(); // Return defaults if no restaurant ID
    }

    // Check if we should force a database refresh
    // This can be triggered by a URL parameter or by passing forceDbRefresh=true
    const shouldForceDbRefresh = forceDbRefresh ||
      (typeof window !== 'undefined' && window.location.search.includes('force_db_refresh=true'));

    // Try to get from localStorage first (faster) if not forcing DB refresh
    if (!shouldForceDbRefresh) {
      const storageKey = `pricing_rules_${targetRestaurantId}`;
      const storedRules = localStorage.getItem(storageKey);

      console.log('🔍 Checking localStorage for pricing rules with key:', storageKey);
      console.log('🔍 Found rules in localStorage:', storedRules ? 'YES' : 'NO');

      if (storedRules) {
        try {
          const parsedRules = JSON.parse(storedRules) as PricingRules;
          console.log('💾 Found pricing rules in localStorage');
          console.log('💾 Parsed rules from localStorage:', parsedRules);
          console.log('💾 applyToCategories in parsed rules:', parsedRules.applyToCategories);

          // Ensure applyToCategories exists and has the correct structure
          if (!parsedRules.applyToCategories) {
            console.log('💾 No applyToCategories found, creating default structure');
            parsedRules.applyToCategories = {
              food: true,  // Default to true for food
              drinks: false // Default to false for drinks
            };
          } else {
            console.log('💾 applyToCategories found:', parsedRules.applyToCategories);
            console.log('💾 food value:', parsedRules.applyToCategories.food);
            console.log('💾 drinks value:', parsedRules.applyToCategories.drinks);

            // Ensure both food and drinks properties exist
            if (parsedRules.applyToCategories.food === undefined) {
              console.log('💾 food value is undefined, setting to default (true)');
              parsedRules.applyToCategories.food = true; // Default to true for food
            }
            if (parsedRules.applyToCategories.drinks === undefined) {
              console.log('💾 drinks value is undefined, setting to default (false)');
              parsedRules.applyToCategories.drinks = false; // Default to false for drinks
            }
          }

          // Handle legacy flags (for backward compatibility)
          const legacyRules = parsedRules as PricingRules & {
            applyToFoodItems?: boolean;
            applyToDrinkItems?: boolean;
          };
          const lf = legacyRules.applyToFoodItems;
          const ld = legacyRules.applyToDrinkItems;
          if (lf !== undefined || ld !== undefined) {
            console.log('💾 Found legacy flags in localStorage rules:', { applyToFoodItems: lf, applyToDrinkItems: ld });
            parsedRules.applyToCategories = {
              food: lf !== undefined ? Boolean(lf) : parsedRules.applyToCategories?.food ?? true,
              drinks: ld !== undefined ? Boolean(ld) : parsedRules.applyToCategories?.drinks ?? false,
            };
          }

          // Normalize applyToCategories values to boolean
          {
            const atc = parsedRules.applyToCategories;
            // Handle possible string values by converting them to boolean
            const normalizedFood = typeof atc.food === 'string' ? atc.food === 'true' : Boolean(atc.food);
            const normalizedDrinks = typeof atc.drinks === 'string' ? atc.drinks === 'true' : Boolean(atc.drinks);
            console.log('💾 Normalized applyToCategories from localStorage:', { food: normalizedFood, drinks: normalizedDrinks });
            parsedRules.applyToCategories.food = normalizedFood;
            parsedRules.applyToCategories.drinks = normalizedDrinks;
          }

          // Final check of values after normalization
          console.log('💾 Final applyToCategories values from localStorage:', parsedRules.applyToCategories);

          // If not forcing DB refresh, return the localStorage values
          if (!shouldForceDbRefresh) {
            return parsedRules;
          }
        } catch (e) {
          console.warn('Error parsing stored pricing rules:', e);
        }
      }
    } else {
      console.log('🔄 Forcing database refresh for pricing rules');
    }

    // If we get here, either:
    // 1. We're forcing a DB refresh
    // 2. There were no rules in localStorage
    // 3. There was an error parsing the localStorage rules
    // So we'll try to get from the database
    console.log('💻 Checking database for pricing rules');

    // Define type for restaurant with pricing rules
    interface RestaurantWithRules {
      pricing_rules?: string | PricingRules;
    }

    console.log('💻 Running DB query for restaurant ID:', targetRestaurantId);

    // Try the RPC function first, then fall back to direct query
    let data, error;
    try {
      const rpcResult = await supabase
        .rpc('get_dynamic_pricing_settings', { restaurant_id_param: targetRestaurantId });
      data = rpcResult.data;
      error = rpcResult.error;
    } catch (rpcError) {
      console.warn('⚠️ RPC function not available, falling back to direct query:', rpcError);
      // Fall back to direct query using the new architecture
      const directResult = await supabase
        .from('restaurant_details')
        .select('pricing_rules')
        .eq('business_id', targetRestaurantId)
        .single();

      if (directResult.error && directResult.error.code !== 'PGRST116') {
        error = directResult.error;
      } else if (directResult.data) {
        data = [{ pricing_rules: directResult.data.pricing_rules }];
      } else {
        // No restaurant_details found, return default rules
        return getDefaultPricingRules();
      }
    }

    if (error) {
      console.warn('Error fetching pricing rules from database:', error);
      console.warn('Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      return getDefaultPricingRules();
    }
    
    console.log('💻 DB query response:', data);

    if (data && data.length > 0 && data[0].pricing_rules) {
      try {
        const dbRules = data[0].pricing_rules;
        console.log('💾 Found pricing rules in database:', dbRules);
        console.log('💾 Rules type:', typeof dbRules);

        // Parse rules if they're stored as a string
        const parsedRules = typeof dbRules === 'string'
          ? JSON.parse(dbRules) as PricingRules
          : dbRules as PricingRules;

        console.log('💾 Parsed rules from database:', parsedRules);
        console.log('💾 applyToCategories in database rules:', parsedRules.applyToCategories);

        // Ensure applyToCategories exists and has the correct structure
        if (!parsedRules.applyToCategories) {
          console.log('💾 No applyToCategories found in database, creating default structure');
          parsedRules.applyToCategories = {
            food: true,  // Default to true for food
            drinks: false // Default to false for drinks
          };
        } else {
          console.log('💾 applyToCategories found in database:', parsedRules.applyToCategories);
          console.log('💾 food value from database:', parsedRules.applyToCategories.food);
          console.log('💾 drinks value from database:', parsedRules.applyToCategories.drinks);

          // Ensure both food and drinks properties exist
          if (parsedRules.applyToCategories.food === undefined) {
            console.log('💾 food value is undefined in database, setting to default (true)');
            parsedRules.applyToCategories.food = true; // Default to true for food
          }
          if (parsedRules.applyToCategories.drinks === undefined) {
            console.log('💾 drinks value is undefined in database, setting to default (false)');
            parsedRules.applyToCategories.drinks = false; // Default to false for drinks
          }
        }
        // Normalize legacy flags for per-category toggles
        const legacyRules = parsedRules as PricingRules & {
          applyToFoodItems?: boolean;
          applyToDrinkItems?: boolean;
        };
        const lf = legacyRules.applyToFoodItems;
        const ld = legacyRules.applyToDrinkItems;
        if (lf !== undefined || ld !== undefined) {
          console.log('💾 Found legacy flags in rules:', { applyToFoodItems: lf, applyToDrinkItems: ld });
          parsedRules.applyToCategories = {
            food: lf !== undefined ? Boolean(lf) : parsedRules.applyToCategories?.food ?? true,
            drinks: ld !== undefined ? Boolean(ld) : parsedRules.applyToCategories?.drinks ?? false,
          };
        }

        // Normalize applyToCategories values to boolean
        {
          const atc = parsedRules.applyToCategories;
          const normalizedFood = typeof atc.food === 'string' ? atc.food === 'true' : Boolean(atc.food);
          const normalizedDrinks = typeof atc.drinks === 'string' ? atc.drinks === 'true' : Boolean(atc.drinks);
          console.log('💾 Normalized applyToCategories from database:', { 
            food: normalizedFood, 
            drinks: normalizedDrinks,
            originalFoodType: typeof atc.food,
            originalDrinksType: typeof atc.drinks
          });
          parsedRules.applyToCategories.food = normalizedFood;
          parsedRules.applyToCategories.drinks = normalizedDrinks;
        }

        // Final check of values after normalization
        console.log('💾 Final applyToCategories values:', parsedRules.applyToCategories);

        // Store in localStorage for future use
        const storageKey = `pricing_rules_${targetRestaurantId}`;
        localStorage.setItem(storageKey, JSON.stringify(parsedRules));
        console.log('💾 Found pricing rules in database');
        return parsedRules;
      } catch (e) {
        console.warn('Error parsing database pricing rules:', e);
      }
    }

    // If no rules found, return default rules
    console.log('⚠️ No custom pricing rules found, using defaults');
    return getDefaultPricingRules();
  } catch (error) {
    console.error('Error getting pricing rules:', error);
    console.warn('getPricingRules failed—using defaults:', error);
    return getDefaultPricingRules();
  }
};

/**
 * Get default pricing rules
 */
const getDefaultPricingRules = (): PricingRules => {
  // This function provides default rules when DB rules can't be loaded
  // In production, if this is being called unexpectedly, it means there's an issue
  const defaultRules: PricingRules = {
    highTraffic: {
      percentage: 10, // 10% increase
      threshold: 0.8, // 80% capacity
    },
    mediumTraffic: {
      percentage: 5, // 5% increase
      threshold: 0.5, // 50% capacity
    },
    lowTraffic: {
      percentage: -5, // 5% decrease
      threshold: 0.3, // 30% capacity
    },
    // Include applyToCategories with conservative default values
    // These defaults should be conservative since they're only used when DB rules can't be loaded
    applyToCategories: {
      food: false,    // Conservative default - don't apply to food unless explicitly enabled
      drinks: false   // Conservative default - don't apply to drinks unless explicitly enabled
    },
    confidenceThreshold: 0.6,
    updatedAt: new Date().toISOString()
  };

// Note: getCurrentTrafficEstimate has been replaced by getCurrentTraffic
// This function is kept for reference but is no longer used

  console.log('🔥 PRODUCTION getDefaultPricingRules called - returning:', defaultRules);
  console.log('🔥 PRODUCTION applyToCategories in defaults:', defaultRules.applyToCategories);
  console.log('⚠️ WARNING: Using default pricing rules instead of database rules - this may indicate a database connectivity issue');
  
  return defaultRules;
};

/**
 * Get time of day string
 */
export const getCurrentTimeOfDay = (): string => {
  const hour = new Date().getHours();

  if (hour >= 5 && hour < 11) return 'breakfast';
  if (hour >= 11 && hour < 15) return 'lunch';
  if (hour >= 15 && hour < 17) return 'afternoon';
  if (hour >= 17 && hour < 22) return 'dinner';
  return 'night';
};

/**
 * Fallback method to determine if current time is a typical peak hour
 */
export const isTypicalPeakHour = (): boolean => {
  const now = new Date();
  const hour = now.getHours();
  const day = now.getDay();

  // Weekend (Saturday and Sunday)
  if (day === 0 || day === 6) {
    // Weekend brunch/lunch (11am-2pm) and dinner (6pm-9pm)
    return (hour >= 11 && hour < 14) || (hour >= 18 && hour < 21);
  }

  // Weekday lunch (12pm-2pm) and dinner (6pm-8pm)
  return (hour >= 12 && hour < 14) || (hour >= 18 && hour < 20);
};

/**
 * Interface for custom pricing rules
 */
export interface PricingRules {
  highTraffic?: {
    percentage: number;
    threshold: number;
  };
  mediumTraffic?: {
    percentage: number;
    threshold: number;
  };
  lowTraffic?: {
    percentage: number;
    threshold: number;
  };
  applyToCategories?: {
    food: boolean;
    drinks: boolean;
  };
  confidenceThreshold?: number;
  updatedAt?: string;
}

/**
 * Debug function to help diagnose dynamic pricing issues
 * This can be called from the browser console
 */
// Expose the debug functions to the browser console
if (typeof window !== 'undefined') {
  // Define a proper interface for the window object
  interface CustomWindow extends Window {
    debugDynamicPricing: (restaurantId: string) => Promise<{
      restaurantId: string;
      isPricingEnabled: boolean;
      localStorageRules: PricingRules | null;
      databaseRules: PricingRules;
      timestamp: string;
      error?: string;
    }>;
    clearPricingRulesCache: (restaurantId: string) => void;
    forcePricingRulesRefresh: (restaurantId: string) => Promise<PricingRules>;
    updatePricingRules: (restaurantId: string, rules: PricingRules) => Promise<boolean>;
    fixDrinksPricing: (restaurantId: string) => Promise<boolean>;
  }

  // Add the debug functions to the window object
  (window as unknown as CustomWindow).debugDynamicPricing = (restaurantId: string) => {
    console.log('🔍 Running dynamic pricing diagnostics...');
    return debugDynamicPricing(restaurantId);
  };

  // Add the cache clearing function to the window object
  (window as unknown as CustomWindow).clearPricingRulesCache = (restaurantId: string) => {
    console.log('🧹 Clearing pricing rules cache...');
    clearPricingRulesCache(restaurantId);
    console.log('✅ Cache cleared. Refresh the page to see the changes.');
  };

  // Add the force refresh function to the window object
  (window as unknown as CustomWindow).forcePricingRulesRefresh = async (restaurantId: string) => {
    console.log('🔄 Forcing refresh of pricing rules...');
    const rules = await forcePricingRulesRefresh(restaurantId);
    console.log('✅ Rules refreshed:', rules);
    return rules;
  };

  // Add the update pricing rules function to the window object
  (window as unknown as CustomWindow).updatePricingRules = async (restaurantId: string, rules: PricingRules) => {
    console.log('🔄 Updating pricing rules...');
    const success = await updatePricingRules(restaurantId, rules);
    if (success) {
      console.log('✅ Rules updated successfully!');
    } else {
      console.error('❌ Failed to update rules.');
    }
    return success;
  };

  // Add a debug function to toggle drink pricing ON (this is for debugging only)
  (window as unknown as CustomWindow).fixDrinksPricing = async (restaurantId: string) => {
    console.log('🔧 DEBUG TOOL: ENABLING DRINKS PRICING...');
    console.log('Note: This is a debug tool that overrides your settings to turn ON drink pricing');

    try {
      // Get current rules
      const currentRules = await forcePricingRulesRefresh(restaurantId);
      console.log('Current rules:', currentRules);
      
      // Preserve the original food setting, but force drinks to true
      const applyToFood = currentRules?.applyToCategories?.food ?? true;
      
      // Create updated rules with drinks explicitly set to true
      const updatedRules = {
        ...currentRules,
        applyToCategories: {
          food: applyToFood,  // Keep the current food setting
          drinks: true       // Force drinks pricing ON
        }
      };

      // Update the database and clear caches
      const success = await updatePricingRules(restaurantId, updatedRules);

      if (success) {
        console.log('✅ DRINKS PRICING FIXED! Refresh the page to see the changes.');
        return true;
      } else {
        console.error('❌ Failed to fix drinks pricing.');
        return false;
      }
    } catch (error) {
      console.error('Error fixing drinks pricing:', error);
      return false;
    }
  };
}

export const debugDynamicPricing = async (restaurantId: string): Promise<{
  restaurantId: string;
  isPricingEnabled: boolean;
  localStorageRules: PricingRules | null;
  databaseRules: PricingRules;
  timestamp: string;
  error?: string;
}> => {
  console.log('🔍 DEBUG: Dynamic Pricing Diagnostics');
  console.log('🏪 Restaurant ID:', restaurantId);

  try {
    // Check if dynamic pricing is enabled
    const isPricingEnabled = await isDynamicPricingEnabled(restaurantId);
    console.log(`🔌 Dynamic pricing enabled: ${isPricingEnabled}`);

    // Get pricing rules from localStorage
    const storageKey = `pricing_rules_${restaurantId}`;
    const storedRules = localStorage.getItem(storageKey);
    console.log('📦 Rules in localStorage:', storedRules ? 'YES' : 'NO');

    if (storedRules) {
      try {
        const parsedRules = JSON.parse(storedRules) as PricingRules;
        console.log('📦 Parsed rules from localStorage:', parsedRules);
        console.log('📦 applyToCategories in localStorage:', parsedRules.applyToCategories);
      } catch (e) {
        console.warn('Error parsing stored rules:', e);
      }
    }

    // Get pricing rules from database
    console.log('🔍 Fetching rules from database...');
    const dbRules = await getPricingRules(restaurantId, null, true);
    console.log('💾 Rules from database:', dbRules);
    console.log('💾 applyToCategories in database:', dbRules.applyToCategories);

    // Return all diagnostic information
    return {
      restaurantId,
      isPricingEnabled,
      localStorageRules: storedRules ? JSON.parse(storedRules) : null,
      databaseRules: dbRules,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error in diagnostics:', error);
    // Return a valid object with error information
    return {
      restaurantId,
      isPricingEnabled: false,
      localStorageRules: null,
      databaseRules: getDefaultPricingRules(),
      timestamp: new Date().toISOString(),
      error: String(error)
    };
  }
};
