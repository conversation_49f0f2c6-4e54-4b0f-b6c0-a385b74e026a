
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { RestaurantProvider } from "@/contexts/RestaurantContext";
import Index from "./pages/Index";
import Menu from "./pages/Menu";
import MenuWithExpandableCards from "./pages/MenuWithExpandableCards";
import OrderConfirmation from "./pages/OrderConfirmation";
import OrderStatus from "./pages/OrderStatus";
import AdminLogin from "./pages/AdminLogin";
import ExpandableCardDemo from "./pages/ExpandableCardDemo";
import TestQrMenu from "./pages/TestQrMenu";
import AdminDashboard from "./pages/AdminDashboard";
import MenuManagement from "./pages/MenuManagement";
import MenusManagement from "./pages/MenusManagement";
import TableManagement from "./pages/TableManagement";
import OrderManagement from "./pages/OrderManagement";
import Analytics from "./pages/Analytics";
import Settings from "./pages/Settings";
import StaffRequests from "./pages/StaffRequests";
import NotFound from "./pages/NotFound";
import ProtectedRoute from "./components/ProtectedRoute";
import { OrderHistoryDrawer } from "./components/OrderHistoryDrawer";
import { OrderHistoryDrawerWrapper } from "./components/OrderHistoryDrawerWrapper";
import { CookieConsent } from "./components/CookieConsent";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <LanguageProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <RestaurantProvider>
                {/* Order History Drawer that only appears on customer-facing pages */}
                <OrderHistoryDrawerWrapper />
                
                <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/menu" element={<MenuWithExpandableCards />} />
                <Route path="/menu-original" element={<Menu />} />
                <Route path="/test-qr-menu" element={<TestQrMenu />} />
                <Route path="/order-confirmation" element={<OrderConfirmation />} />
                <Route path="/order-status/:orderId" element={<OrderStatus />} />
                <Route path="/admin" element={<AdminLogin />} />
                <Route path="/expandable-card-demo" element={<ExpandableCardDemo />} />
                <Route path="/admin/dashboard" element={
                  <ProtectedRoute>
                    <AdminDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/admin/menu-items" element={
                  <ProtectedRoute>
                    <MenuManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/menus" element={
                  <ProtectedRoute>
                    <MenusManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/tables" element={
                  <ProtectedRoute>
                    <TableManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/orders" element={
                  <ProtectedRoute>
                    <OrderManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/analytics" element={
                  <ProtectedRoute>
                    <Analytics />
                  </ProtectedRoute>
                } />
                <Route path="/admin/settings" element={
                  <ProtectedRoute>
                    <Settings />
                  </ProtectedRoute>
                } />
                <Route path="/admin/staff-requests" element={
                  <ProtectedRoute>
                    <StaffRequests />
                  </ProtectedRoute>
                } />
                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
              </RestaurantProvider>
              
              {/* Cookie consent banner */}
              <CookieConsent />
            </BrowserRouter>
        </LanguageProvider>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
